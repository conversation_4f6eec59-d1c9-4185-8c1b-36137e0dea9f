{"version": 3, "sources": ["../packages/snap/src/debounce.ts", "../packages/snap/src/element.ts", "../packages/snap/src/uid.ts", "../packages/snap/src/snap.ts", "../packages/snap/browser.ts"], "sourcesContent": ["export function debounce<CB extends (...args: any[]) => void>(\n  callback: CB,\n  delay: number\n) {\n  let timer: number | undefined\n  return function <T>(this: T, ...args: Parameters<typeof callback>) {\n    let context = this\n    clearTimeout(timer)\n    timer = setTimeout(() => {\n      timer = undefined\n      callback.apply(context, args)\n    }, delay)\n  }\n}\n", "function removeParentSticky(element: HTMLElement) {\n  const position = getComputedStyle(element).position\n\n  const isSticky = position === 'sticky'\n\n  if (isSticky) {\n    element.style.setProperty('position', 'static')\n    element.dataset.sticky = 'true'\n  }\n\n  if (element.offsetParent) {\n    removeParentSticky(element.offsetParent as HTMLElement)\n  }\n}\n\nfunction addParentSticky(element: HTMLElement) {\n  if (element?.dataset?.sticky === 'true') {\n    element.style.removeProperty('position')\n    delete element.dataset.sticky\n  }\n\n  if (element.offsetParent) {\n    addParentSticky(element.offsetParent as HTMLElement)\n  }\n}\n\nfunction offsetTop(element: HTMLElement, accumulator = 0) {\n  const top = accumulator + element.offsetTop\n  if (element.offsetParent) {\n    return offsetTop(element.offsetParent as HTMLElement, top)\n  }\n  return top\n}\n\nfunction offsetLeft(element: HTMLElement, accumulator = 0) {\n  const left = accumulator + element.offsetLeft\n  if (element.offsetParent) {\n    return offsetLeft(element.offsetParent as HTMLElement, left)\n  }\n  return left\n}\n\nfunction scrollTop(element: HTMLElement, accumulator = 0) {\n  const top = accumulator + element.scrollTop\n  if (element.offsetParent) {\n    return scrollTop(element.offsetParent as HTMLElement, top)\n  }\n  return top + window.scrollY\n}\n\nfunction scrollLeft(element: HTMLElement, accumulator = 0) {\n  const left = accumulator + element.scrollLeft\n  if (element.offsetParent) {\n    return scrollLeft(element.offsetParent as HTMLElement, left)\n  }\n  return left + window.scrollX\n}\n\nexport type SnapElementOptions = {\n  align?: string[]\n  ignoreSticky?: boolean\n  ignoreTransform?: boolean\n}\n\ntype Rect = {\n  top: number\n  left: number\n  width: number\n  height: number\n  x: number\n  y: number\n  bottom: number\n  right: number\n  element: HTMLElement\n}\n\nexport class SnapElement {\n  element: HTMLElement\n  options: SnapElementOptions\n  align: string[]\n  // @ts-ignore\n  rect: Rect = {}\n  wrapperResizeObserver: ResizeObserver\n  resizeObserver: ResizeObserver\n\n  constructor(\n    element: HTMLElement,\n    {\n      align = ['start'],\n      ignoreSticky = true,\n      ignoreTransform = false,\n    }: SnapElementOptions = {}\n  ) {\n    this.element = element\n\n    this.options = { align, ignoreSticky, ignoreTransform }\n\n    // this.ignoreSticky = ignoreSticky\n    // this.ignoreTransform = ignoreTransform\n\n    this.align = [align].flat()\n\n    // TODO: assing rect immediately\n\n    this.wrapperResizeObserver = new ResizeObserver(this.onWrapperResize)\n    this.wrapperResizeObserver.observe(document.body)\n    this.onWrapperResize()\n\n    this.resizeObserver = new ResizeObserver(this.onResize)\n    this.resizeObserver.observe(this.element)\n    this.setRect({\n      width: this.element.offsetWidth,\n      height: this.element.offsetHeight,\n    })\n  }\n\n  destroy() {\n    this.wrapperResizeObserver.disconnect()\n    this.resizeObserver.disconnect()\n  }\n\n  setRect({\n    top,\n    left,\n    width,\n    height,\n    element,\n  }: {\n    top?: number\n    left?: number\n    width?: number\n    height?: number\n    element?: HTMLElement\n  } = {}) {\n    top = top ?? this.rect.top\n    left = left ?? this.rect.left\n    width = width ?? this.rect.width\n    height = height ?? this.rect.height\n    element = element ?? this.rect.element\n\n    if (\n      top === this.rect.top &&\n      left === this.rect.left &&\n      width === this.rect.width &&\n      height === this.rect.height &&\n      element === this.rect.element\n    )\n      return\n\n    this.rect.top = top\n    this.rect.y = top\n    this.rect.width = width\n    this.rect.height = height\n    this.rect.left = left\n    this.rect.x = left\n    this.rect.bottom = top + height\n    this.rect.right = left + width\n  }\n\n  onWrapperResize = () => {\n    let top, left\n\n    if (this.options.ignoreSticky) removeParentSticky(this.element)\n    if (this.options.ignoreTransform) {\n      top = offsetTop(this.element)\n      left = offsetLeft(this.element)\n    } else {\n      const rect = this.element.getBoundingClientRect()\n      top = rect.top + scrollTop(this.element)\n      left = rect.left + scrollLeft(this.element)\n    }\n    if (this.options.ignoreSticky) addParentSticky(this.element)\n\n    this.setRect({ top, left })\n  }\n\n  onResize = ([entry]: ResizeObserverEntry[]) => {\n    if (!entry?.borderBoxSize[0]) return\n    const width = entry.borderBoxSize[0].inlineSize\n    const height = entry.borderBoxSize[0].blockSize\n\n    this.setRect({ width, height })\n  }\n}\n", "let index = 0\n\nexport type UID = number\n\nexport function uid(): UID {\n  return index++\n}\n", "import type Lenis from 'lenis'\nimport type { UserData } from 'lenis'\nimport { debounce } from './debounce'\nimport type { SnapElementOptions } from './element'\nimport { SnapElement } from './element'\nimport type { SnapItem, SnapOptions } from './types'\nimport type { UID } from './uid'\nimport { uid } from './uid'\n\n// TODO:\n// - horizontal\n// - fix trackpad snapping too soon due to velocity (fuck Apple)\n// - fix wheel scrolling after limits (see console scroll to)\n// - fix touch scroll, do not snap when not released\n// - arrow, spacebar\n\ntype RequiredPick<T, F extends keyof T> = Omit<T, F> & Required<Pick<T, F>>\n\n/**\n * Snap class to handle the snap functionality\n *\n * @example\n * const snap = new Snap(lenis, {\n *   type: 'mandatory', // 'mandatory', 'proximity'\n *   lerp: 0.1,\n *   duration: 1,\n *   easing: (t) => t,\n *   onSnapStart: (snap) => {\n *     console.log('onSnapStart', snap)\n *   },\n *   onSnapComplete: (snap) => {\n *     console.log('onSnapComplete', snap)\n *   },\n * })\n *\n * snap.add(500) // snap at 500px\n *\n * const removeSnap = snap.add(500)\n *\n * if (someCondition) {\n *   removeSnap()\n * }\n */\nexport class Snap {\n  options: RequiredPick<SnapOptions, 'type' | 'velocityThreshold' | 'debounce'>\n  elements = new Map<UID, SnapElement>()\n  snaps = new Map<UID, SnapItem>()\n  viewport = {\n    width: window.innerWidth,\n    height: window.innerHeight,\n  }\n  isStopped = false\n  onSnapDebounced: () => void\n\n  constructor(\n    private lenis: Lenis,\n    {\n      type = 'mandatory',\n      lerp,\n      easing,\n      duration,\n      velocityThreshold = 1,\n      debounce: debounceDelay = 0,\n      onSnapStart,\n      onSnapComplete,\n    }: SnapOptions = {}\n  ) {\n    this.options = {\n      type,\n      lerp,\n      easing,\n      duration,\n      velocityThreshold,\n      debounce: debounceDelay,\n      onSnapStart,\n      onSnapComplete,\n    }\n\n    this.onWindowResize()\n    window.addEventListener('resize', this.onWindowResize, false)\n\n    this.onSnapDebounced = debounce(this.onSnap, this.options.debounce)\n\n    this.lenis.on('scroll', this.onScroll)\n  }\n\n  /**\n   * Destroy the snap instance\n   */\n  destroy() {\n    this.lenis.off('scroll', this.onScroll)\n    window.removeEventListener('resize', this.onWindowResize, false)\n    this.elements.forEach((element) => element.destroy())\n  }\n\n  /**\n   * Start the snap after it has been stopped\n   */\n  start() {\n    this.isStopped = false\n  }\n\n  /**\n   * Stop the snap\n   */\n  stop() {\n    this.isStopped = true\n  }\n\n  /**\n   * Add a snap to the snap instance\n   *\n   * @param value The value to snap to\n   * @param userData User data that will be forwarded through the snap event\n   * @returns Unsubscribe function\n   */\n  add(value: number, userData: UserData = {}) {\n    const id = uid()\n\n    this.snaps.set(id, { value, userData })\n\n    return () => this.remove(id)\n  }\n\n  /**\n   * Remove a snap from the snap instance\n   *\n   * @param id The snap id of the snap to remove\n   */\n  remove(id: UID) {\n    this.snaps.delete(id)\n  }\n\n  /**\n   * Add an element to the snap instance\n   *\n   * @param element The element to add\n   * @param options The options for the element\n   * @returns Unsubscribe function\n   */\n  addElement(element: HTMLElement, options = {} as SnapElementOptions) {\n    const id = uid()\n\n    this.elements.set(id, new SnapElement(element, options))\n\n    return () => this.removeElement(id)\n  }\n\n  /**\n   * Remove an element from the snap instance\n   *\n   * @param id The snap id of the snap element to remove\n   */\n  removeElement(id: UID) {\n    this.elements.delete(id)\n  }\n\n  private onWindowResize = () => {\n    this.viewport.width = window.innerWidth\n    this.viewport.height = window.innerHeight\n  }\n\n  private onScroll = ({\n    // scroll,\n    // limit,\n    lastVelocity,\n    velocity,\n    // isScrolling,\n    userData,\n  }: // isHorizontal,\n  Lenis) => {\n    if (this.isStopped) return\n\n    // return\n    const isDecelerating = Math.abs(lastVelocity) > Math.abs(velocity)\n    const isTurningBack =\n      Math.sign(lastVelocity) !== Math.sign(velocity) && velocity !== 0\n\n    if (\n      Math.abs(velocity) < this.options.velocityThreshold &&\n      // !isTouching &&\n      isDecelerating &&\n      !isTurningBack &&\n      userData?.initiator !== 'snap'\n    ) {\n      this.onSnapDebounced()\n    }\n  }\n\n  private onSnap = () => {\n    let { scroll, isHorizontal } = this.lenis\n    scroll = Math.ceil(this.lenis.scroll)\n\n    let snaps = [...this.snaps.values()] as SnapItem[]\n\n    this.elements.forEach(({ rect, align }) => {\n      let value: number | undefined\n\n      align.forEach((align) => {\n        if (align === 'start') {\n          value = rect.top\n        } else if (align === 'center') {\n          value = isHorizontal\n            ? rect.left + rect.width / 2 - this.viewport.width / 2\n            : rect.top + rect.height / 2 - this.viewport.height / 2\n        } else if (align === 'end') {\n          value = isHorizontal\n            ? rect.left + rect.width - this.viewport.width\n            : rect.top + rect.height - this.viewport.height\n        }\n\n        if (typeof value === 'number') {\n          snaps.push({ value: Math.ceil(value), userData: {} })\n        }\n      })\n    })\n\n    snaps = snaps.sort((a, b) => Math.abs(a.value) - Math.abs(b.value))\n\n    let prevSnap = snaps.findLast(({ value }) => value <= scroll)\n    if (prevSnap === undefined) prevSnap = snaps[0]!\n    const distanceToPrevSnap = Math.abs(scroll - prevSnap.value)\n\n    let nextSnap = snaps.find(({ value }) => value >= scroll)\n    if (nextSnap === undefined) nextSnap = snaps[snaps.length - 1]!\n    const distanceToNextSnap = Math.abs(scroll - nextSnap.value)\n\n    const snap = distanceToPrevSnap < distanceToNextSnap ? prevSnap : nextSnap\n\n    const distance = Math.abs(scroll - snap.value)\n\n    if (\n      this.options.type === 'mandatory' ||\n      (this.options.type === 'proximity' &&\n        distance <=\n          (isHorizontal\n            ? this.lenis.dimensions.width\n            : this.lenis.dimensions.height))\n    ) {\n      // this.__isScrolling = true\n      // this.onSnapStart?.(snap)\n\n      // console.log('scroll to')\n\n      this.lenis.scrollTo(snap.value, {\n        lerp: this.options.lerp,\n        easing: this.options.easing,\n        duration: this.options.duration,\n        userData: { initiator: 'snap' },\n        onStart: () => {\n          this.options.onSnapStart?.(snap)\n        },\n        onComplete: () => {\n          this.options.onSnapComplete?.(snap)\n        },\n      })\n    }\n\n    // console.timeEnd('scroll')\n  }\n}\n", "// This file serves as an entry point for the package\nimport { Snap } from './src/snap'\nglobalThis.Snap = Snap\n"], "mappings": ";AAAO,SAAS,SACd,UACA,OACA;AACA,MAAI;AACJ,SAAO,YAAyB,MAAmC;AACjE,QAAI,UAAU;AACd,iBAAa,KAAK;AAClB,YAAQ,WAAW,MAAM;AACvB,cAAQ;AACR,eAAS,MAAM,SAAS,IAAI;AAAA,IAC9B,GAAG,KAAK;AAAA,EACV;AACF;;;ACbA,SAAS,mBAAmB,SAAsB;AAChD,QAAM,WAAW,iBAAiB,OAAO,EAAE;AAE3C,QAAM,WAAW,aAAa;AAE9B,MAAI,UAAU;AACZ,YAAQ,MAAM,YAAY,YAAY,QAAQ;AAC9C,YAAQ,QAAQ,SAAS;AAAA,EAC3B;AAEA,MAAI,QAAQ,cAAc;AACxB,uBAAmB,QAAQ,YAA2B;AAAA,EACxD;AACF;AAEA,SAAS,gBAAgB,SAAsB;AAC7C,MAAI,SAAS,SAAS,WAAW,QAAQ;AACvC,YAAQ,MAAM,eAAe,UAAU;AACvC,WAAO,QAAQ,QAAQ;AAAA,EACzB;AAEA,MAAI,QAAQ,cAAc;AACxB,oBAAgB,QAAQ,YAA2B;AAAA,EACrD;AACF;AAEA,SAAS,UAAU,SAAsB,cAAc,GAAG;AACxD,QAAM,MAAM,cAAc,QAAQ;AAClC,MAAI,QAAQ,cAAc;AACxB,WAAO,UAAU,QAAQ,cAA6B,GAAG;AAAA,EAC3D;AACA,SAAO;AACT;AAEA,SAAS,WAAW,SAAsB,cAAc,GAAG;AACzD,QAAM,OAAO,cAAc,QAAQ;AACnC,MAAI,QAAQ,cAAc;AACxB,WAAO,WAAW,QAAQ,cAA6B,IAAI;AAAA,EAC7D;AACA,SAAO;AACT;AAEA,SAAS,UAAU,SAAsB,cAAc,GAAG;AACxD,QAAM,MAAM,cAAc,QAAQ;AAClC,MAAI,QAAQ,cAAc;AACxB,WAAO,UAAU,QAAQ,cAA6B,GAAG;AAAA,EAC3D;AACA,SAAO,MAAM,OAAO;AACtB;AAEA,SAAS,WAAW,SAAsB,cAAc,GAAG;AACzD,QAAM,OAAO,cAAc,QAAQ;AACnC,MAAI,QAAQ,cAAc;AACxB,WAAO,WAAW,QAAQ,cAA6B,IAAI;AAAA,EAC7D;AACA,SAAO,OAAO,OAAO;AACvB;AAoBO,IAAM,cAAN,MAAkB;AAAA,EACvB;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA,OAAa,CAAC;AAAA,EACd;AAAA,EACA;AAAA,EAEA,YACE,SACA;AAAA,IACE,QAAQ,CAAC,OAAO;AAAA,IAChB,eAAe;AAAA,IACf,kBAAkB;AAAA,EACpB,IAAwB,CAAC,GACzB;AACA,SAAK,UAAU;AAEf,SAAK,UAAU,EAAE,OAAO,cAAc,gBAAgB;AAKtD,SAAK,QAAQ,CAAC,KAAK,EAAE,KAAK;AAI1B,SAAK,wBAAwB,IAAI,eAAe,KAAK,eAAe;AACpE,SAAK,sBAAsB,QAAQ,SAAS,IAAI;AAChD,SAAK,gBAAgB;AAErB,SAAK,iBAAiB,IAAI,eAAe,KAAK,QAAQ;AACtD,SAAK,eAAe,QAAQ,KAAK,OAAO;AACxC,SAAK,QAAQ;AAAA,MACX,OAAO,KAAK,QAAQ;AAAA,MACpB,QAAQ,KAAK,QAAQ;AAAA,IACvB,CAAC;AAAA,EACH;AAAA,EAEA,UAAU;AACR,SAAK,sBAAsB,WAAW;AACtC,SAAK,eAAe,WAAW;AAAA,EACjC;AAAA,EAEA,QAAQ;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAMI,CAAC,GAAG;AACN,UAAM,OAAO,KAAK,KAAK;AACvB,WAAO,QAAQ,KAAK,KAAK;AACzB,YAAQ,SAAS,KAAK,KAAK;AAC3B,aAAS,UAAU,KAAK,KAAK;AAC7B,cAAU,WAAW,KAAK,KAAK;AAE/B,QACE,QAAQ,KAAK,KAAK,OAClB,SAAS,KAAK,KAAK,QACnB,UAAU,KAAK,KAAK,SACpB,WAAW,KAAK,KAAK,UACrB,YAAY,KAAK,KAAK;AAEtB;AAEF,SAAK,KAAK,MAAM;AAChB,SAAK,KAAK,IAAI;AACd,SAAK,KAAK,QAAQ;AAClB,SAAK,KAAK,SAAS;AACnB,SAAK,KAAK,OAAO;AACjB,SAAK,KAAK,IAAI;AACd,SAAK,KAAK,SAAS,MAAM;AACzB,SAAK,KAAK,QAAQ,OAAO;AAAA,EAC3B;AAAA,EAEA,kBAAkB,MAAM;AACtB,QAAI,KAAK;AAET,QAAI,KAAK,QAAQ,aAAc,oBAAmB,KAAK,OAAO;AAC9D,QAAI,KAAK,QAAQ,iBAAiB;AAChC,YAAM,UAAU,KAAK,OAAO;AAC5B,aAAO,WAAW,KAAK,OAAO;AAAA,IAChC,OAAO;AACL,YAAM,OAAO,KAAK,QAAQ,sBAAsB;AAChD,YAAM,KAAK,MAAM,UAAU,KAAK,OAAO;AACvC,aAAO,KAAK,OAAO,WAAW,KAAK,OAAO;AAAA,IAC5C;AACA,QAAI,KAAK,QAAQ,aAAc,iBAAgB,KAAK,OAAO;AAE3D,SAAK,QAAQ,EAAE,KAAK,KAAK,CAAC;AAAA,EAC5B;AAAA,EAEA,WAAW,CAAC,CAAC,KAAK,MAA6B;AAC7C,QAAI,CAAC,OAAO,cAAc,CAAC,EAAG;AAC9B,UAAM,QAAQ,MAAM,cAAc,CAAC,EAAE;AACrC,UAAM,SAAS,MAAM,cAAc,CAAC,EAAE;AAEtC,SAAK,QAAQ,EAAE,OAAO,OAAO,CAAC;AAAA,EAChC;AACF;;;ACvLA,IAAI,QAAQ;AAIL,SAAS,MAAW;AACzB,SAAO;AACT;;;ACqCO,IAAM,OAAN,MAAW;AAAA,EAWhB,YACU,OACR;AAAA,IACE,OAAO;AAAA,IACP;AAAA,IACA;AAAA,IACA;AAAA,IACA,oBAAoB;AAAA,IACpB,UAAU,gBAAgB;AAAA,IAC1B;AAAA,IACA;AAAA,EACF,IAAiB,CAAC,GAClB;AAXQ;AAYR,SAAK,UAAU;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,UAAU;AAAA,MACV;AAAA,MACA;AAAA,IACF;AAEA,SAAK,eAAe;AACpB,WAAO,iBAAiB,UAAU,KAAK,gBAAgB,KAAK;AAE5D,SAAK,kBAAkB,SAAS,KAAK,QAAQ,KAAK,QAAQ,QAAQ;AAElE,SAAK,MAAM,GAAG,UAAU,KAAK,QAAQ;AAAA,EACvC;AAAA,EAxCA;AAAA,EACA,WAAW,oBAAI,IAAsB;AAAA,EACrC,QAAQ,oBAAI,IAAmB;AAAA,EAC/B,WAAW;AAAA,IACT,OAAO,OAAO;AAAA,IACd,QAAQ,OAAO;AAAA,EACjB;AAAA,EACA,YAAY;AAAA,EACZ;AAAA;AAAA;AAAA;AAAA,EAqCA,UAAU;AACR,SAAK,MAAM,IAAI,UAAU,KAAK,QAAQ;AACtC,WAAO,oBAAoB,UAAU,KAAK,gBAAgB,KAAK;AAC/D,SAAK,SAAS,QAAQ,CAAC,YAAY,QAAQ,QAAQ,CAAC;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ;AACN,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO;AACL,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,IAAI,OAAe,WAAqB,CAAC,GAAG;AAC1C,UAAM,KAAK,IAAI;AAEf,SAAK,MAAM,IAAI,IAAI,EAAE,OAAO,SAAS,CAAC;AAEtC,WAAO,MAAM,KAAK,OAAO,EAAE;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,IAAS;AACd,SAAK,MAAM,OAAO,EAAE;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,WAAW,SAAsB,UAAU,CAAC,GAAyB;AACnE,UAAM,KAAK,IAAI;AAEf,SAAK,SAAS,IAAI,IAAI,IAAI,YAAY,SAAS,OAAO,CAAC;AAEvD,WAAO,MAAM,KAAK,cAAc,EAAE;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc,IAAS;AACrB,SAAK,SAAS,OAAO,EAAE;AAAA,EACzB;AAAA,EAEQ,iBAAiB,MAAM;AAC7B,SAAK,SAAS,QAAQ,OAAO;AAC7B,SAAK,SAAS,SAAS,OAAO;AAAA,EAChC;AAAA,EAEQ,WAAW,CAAC;AAAA;AAAA;AAAA,IAGlB;AAAA,IACA;AAAA;AAAA,IAEA;AAAA,EACF,MACU;AACR,QAAI,KAAK,UAAW;AAGpB,UAAM,iBAAiB,KAAK,IAAI,YAAY,IAAI,KAAK,IAAI,QAAQ;AACjE,UAAM,gBACJ,KAAK,KAAK,YAAY,MAAM,KAAK,KAAK,QAAQ,KAAK,aAAa;AAElE,QACE,KAAK,IAAI,QAAQ,IAAI,KAAK,QAAQ;AAAA,IAElC,kBACA,CAAC,iBACD,UAAU,cAAc,QACxB;AACA,WAAK,gBAAgB;AAAA,IACvB;AAAA,EACF;AAAA,EAEQ,SAAS,MAAM;AACrB,QAAI,EAAE,QAAQ,aAAa,IAAI,KAAK;AACpC,aAAS,KAAK,KAAK,KAAK,MAAM,MAAM;AAEpC,QAAI,QAAQ,CAAC,GAAG,KAAK,MAAM,OAAO,CAAC;AAEnC,SAAK,SAAS,QAAQ,CAAC,EAAE,MAAM,MAAM,MAAM;AACzC,UAAI;AAEJ,YAAM,QAAQ,CAACA,WAAU;AACvB,YAAIA,WAAU,SAAS;AACrB,kBAAQ,KAAK;AAAA,QACf,WAAWA,WAAU,UAAU;AAC7B,kBAAQ,eACJ,KAAK,OAAO,KAAK,QAAQ,IAAI,KAAK,SAAS,QAAQ,IACnD,KAAK,MAAM,KAAK,SAAS,IAAI,KAAK,SAAS,SAAS;AAAA,QAC1D,WAAWA,WAAU,OAAO;AAC1B,kBAAQ,eACJ,KAAK,OAAO,KAAK,QAAQ,KAAK,SAAS,QACvC,KAAK,MAAM,KAAK,SAAS,KAAK,SAAS;AAAA,QAC7C;AAEA,YAAI,OAAO,UAAU,UAAU;AAC7B,gBAAM,KAAK,EAAE,OAAO,KAAK,KAAK,KAAK,GAAG,UAAU,CAAC,EAAE,CAAC;AAAA,QACtD;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAED,YAAQ,MAAM,KAAK,CAAC,GAAG,MAAM,KAAK,IAAI,EAAE,KAAK,IAAI,KAAK,IAAI,EAAE,KAAK,CAAC;AAElE,QAAI,WAAW,MAAM,SAAS,CAAC,EAAE,MAAM,MAAM,SAAS,MAAM;AAC5D,QAAI,aAAa,OAAW,YAAW,MAAM,CAAC;AAC9C,UAAM,qBAAqB,KAAK,IAAI,SAAS,SAAS,KAAK;AAE3D,QAAI,WAAW,MAAM,KAAK,CAAC,EAAE,MAAM,MAAM,SAAS,MAAM;AACxD,QAAI,aAAa,OAAW,YAAW,MAAM,MAAM,SAAS,CAAC;AAC7D,UAAM,qBAAqB,KAAK,IAAI,SAAS,SAAS,KAAK;AAE3D,UAAM,OAAO,qBAAqB,qBAAqB,WAAW;AAElE,UAAM,WAAW,KAAK,IAAI,SAAS,KAAK,KAAK;AAE7C,QACE,KAAK,QAAQ,SAAS,eACrB,KAAK,QAAQ,SAAS,eACrB,aACG,eACG,KAAK,MAAM,WAAW,QACtB,KAAK,MAAM,WAAW,SAC9B;AAMA,WAAK,MAAM,SAAS,KAAK,OAAO;AAAA,QAC9B,MAAM,KAAK,QAAQ;AAAA,QACnB,QAAQ,KAAK,QAAQ;AAAA,QACrB,UAAU,KAAK,QAAQ;AAAA,QACvB,UAAU,EAAE,WAAW,OAAO;AAAA,QAC9B,SAAS,MAAM;AACb,eAAK,QAAQ,cAAc,IAAI;AAAA,QACjC;AAAA,QACA,YAAY,MAAM;AAChB,eAAK,QAAQ,iBAAiB,IAAI;AAAA,QACpC;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EAGF;AACF;;;AClQA,WAAW,OAAO;", "names": ["align"]}