import React, { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import SellerLayout from "../../components/seller/SellerLayout";
import { FiUpload, FiPlus, FiX } from "react-icons/fi";
import { createContent, uploadContentFile } from "../../redux/slices/contentSlice";
import SummernoteEditor from "../../components/common/SummernoteEditor";
import "../../styles/AddStrategy.css";

const AddStrategy = () => {
  const dispatch = useDispatch();
  const { isLoading, isError, error } = useSelector((state) => state.content);

  // Form state - Visible fields as per screenshot
  const [formData, setFormData] = useState({
    // Visible fields
    title: "",
    category: "",
    coachName: "", // New field for Coach/Seller/Academy Name
    description: "",
    fileUrl: "",
    aboutCoach: "",
    strategicContent: "",

    // Hidden fields with default values for backend compatibility
    sport: "Other", // Default value - hidden to match UI
    contentType: "Video", // Default value - hidden to match UI
    previewUrl: "", // Hidden - not shown in screenshot
    thumbnailUrl: "", // Hidden - not shown in screenshot
    duration: "", // Hidden - not shown in screenshot
    videoLength: "", // Hidden - not shown in screenshot
    fileSize: "", // Hidden - not shown in screenshot
    tags: [], // Hidden - not shown in screenshot
    difficulty: "Intermediate", // Default value - hidden to match UI
    language: "English", // Default value - hidden to match UI
    prerequisites: [], // Hidden - not shown in screenshot
    learningObjectives: [], // Hidden - not shown in screenshot
    equipment: [], // Hidden - not shown in screenshot
    saleType: "Fixed", // Default value - hidden to match UI
    price: 0, // Default value - hidden to match UI
    allowCustomRequests: false, // Default value - hidden to match UI
    customRequestPrice: "", // Hidden - not shown in screenshot
    status: "Draft", // Default value - hidden to match UI
    visibility: "Public" // Default value - hidden to match UI
  });

  // Array field states - Hidden to match UI screenshot
  // const [newTag, setNewTag] = useState("");
  // const [newPrerequisite, setNewPrerequisite] = useState("");
  // const [newLearningObjective, setNewLearningObjective] = useState("");
  // const [newEquipment, setNewEquipment] = useState("");

  // File upload state
  const [uploadedFile, setUploadedFile] = useState(null);

  // Handle input changes
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  // Handle Summernote changes
  const handleSummernoteChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle array field additions - Hidden to match UI screenshot
  // const addArrayItem = (field, value, setter) => {
  //   if (value.trim()) {
  //     setFormData(prev => ({
  //       ...prev,
  //       [field]: [...prev[field], value.trim()]
  //     }));
  //     setter("");
  //   }
  // };

  // Handle array field removals - Hidden to match UI screenshot
  // const removeArrayItem = (field, index) => {
  //   setFormData(prev => ({
  //     ...prev,
  //     [field]: prev[field].filter((_, i) => i !== index)
  //   }));
  // };

  // Handle file upload
  const handleFileUpload = async (e) => {
    const file = e.target.files[0];
    if (file) {
      setUploadedFile(file);
      const formDataUpload = new FormData();
      formDataUpload.append('file', file);
      formDataUpload.append('type', 'content');

      try {
        const result = await dispatch(uploadContentFile(formDataUpload)).unwrap();
        setFormData(prev => ({
          ...prev,
          fileUrl: result.data.fileUrl,
          fileSize: result.data.fileSize || file.size
        }));
      } catch (error) {
        console.error('File upload failed:', error);
      }
    }
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      // Map category to sport for backend compatibility since sport field is hidden
      const submitData = {
        ...formData,
        sport: formData.category || "Other" // Use category as sport for backend
      };
      await dispatch(createContent(submitData)).unwrap();
      // Reset form or redirect on success
      alert('Content created successfully!');
    } catch (error) {
      console.error('Content creation failed:', error);
    }
  };

  // Handle form reset
  const handleReset = () => {
    setFormData({
      // Visible fields
      title: "",
      category: "",
      coachName: "",
      description: "",
      fileUrl: "",
      aboutCoach: "",
      strategicContent: "",

      // Hidden fields with default values for backend compatibility
      sport: "Other",
      contentType: "Video",
      previewUrl: "",
      thumbnailUrl: "",
      duration: "",
      videoLength: "",
      fileSize: "",
      tags: [],
      difficulty: "Intermediate",
      language: "English",
      prerequisites: [],
      learningObjectives: [],
      equipment: [],
      saleType: "Fixed",
      price: 0,
      allowCustomRequests: false,
      customRequestPrice: "",
      status: "Draft",
      visibility: "Public"
    });
    setUploadedFile(null);
    // Array field resets - Hidden to match UI screenshot
    // setNewTag("");
    // setNewPrerequisite("");
    // setNewLearningObjective("");
    // setNewEquipment("");
  };

  return (
    <SellerLayout>
      <div className="AddStrategy">
        {/* Main Form */}
        <form className="AddStrategy__form" onSubmit={handleSubmit}>
          {/* Strategy Title */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">Strategy Title</label>
            <input
              type="text"
              name="title"
              className="AddStrategy__input"
              placeholder="Add title for New Strategy"
              value={formData.title}
              onChange={handleInputChange}
              required
            />
          </div>

          {/* Select Category */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">Select Category</label>
            <select
              name="category"
              className="AddStrategy__select"
              value={formData.category}
              onChange={handleInputChange}
              required
            >
              <option value="">Select Category</option>
              <option value="Basketball">Basketball</option>
              <option value="Football">Football</option>
              <option value="Soccer">Soccer</option>
              <option value="Baseball">Baseball</option>
              <option value="Tennis">Tennis</option>
              <option value="Golf">Golf</option>
              <option value="Swimming">Swimming</option>
              <option value="Volleyball">Volleyball</option>
              <option value="Running">Running</option>
              <option value="Cycling">Cycling</option>
              <option value="Fitness">Fitness</option>
              <option value="Yoga">Yoga</option>
              <option value="Other">Other</option>
            </select>
          </div>

          {/* Coach/Seller/Academy Name */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">Coach/Seller/Academy Name</label>
            <input
              type="text"
              name="coachName"
              className="AddStrategy__input"
              placeholder="Enter coach, seller, or academy name"
              value={formData.coachName}
              onChange={handleInputChange}
            />
          </div>

          {/* Hidden Sport Field - Using category as sport for backend compatibility */}
          {/* Sport field is hidden to match UI, using category value for sport */}

          {/* Description for Strategy - Rich Text Editor */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">Description for Strategy</label>
            <SummernoteEditor
              value={formData.description}
              onChange={(value) => handleSummernoteChange('description', value)}
              placeholder="Enter a detailed description of your strategy..."
              height={200}
              className="AddStrategy__summernote"
            />
          </div>

          {/* Upload Video/Document */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">Upload Video/Document</label>
            <div className="AddStrategy__upload">
              <input
                type="file"
                id="file-upload"
                className="AddStrategy__file-input"
                accept="video/*,audio/*,.pdf,.doc,.docx"
                onChange={handleFileUpload}
                style={{ display: 'none' }}
              />
              <label htmlFor="file-upload" className="AddStrategy__upload-content">
                <FiUpload className="AddStrategy__upload-icon" />
                <p className="AddStrategy__upload-text">
                  {uploadedFile ? uploadedFile.name : "Drag & drop to upload video/Document"}
                </p>
              </label>
            </div>
          </div>

          {/* About The Coach - Rich Text Editor */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">About The Coach</label>
            <SummernoteEditor
              value={formData.aboutCoach}
              onChange={(value) => handleSummernoteChange('aboutCoach', value)}
              placeholder="Share your background, experience, and expertise..."
              height={200}
              className="AddStrategy__summernote"
            />
          </div>

          {/* Includes Strategic Content - Rich Text Editor */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">Includes Strategic Content</label>
            <SummernoteEditor
              value={formData.strategicContent}
              onChange={(value) => handleSummernoteChange('strategicContent', value)}
              placeholder="Describe what strategic content is included..."
              height={200}
              className="AddStrategy__summernote"
            />
          </div>

          {/* HIDDEN FIELDS - Commented out to match UI screenshot */}

          {/* Difficulty Level - Hidden to match UI screenshot */}
          {/* <div className="AddStrategy__field">
            <label className="AddStrategy__label">Difficulty Level *</label>
            <select
              name="difficulty"
              className="AddStrategy__select"
              value={formData.difficulty}
              onChange={handleInputChange}
              required
            >
              <option value="">Select Difficulty</option>
              <option value="Beginner">Beginner</option>
              <option value="Intermediate">Intermediate</option>
              <option value="Advanced">Advanced</option>
              <option value="Professional">Professional</option>
            </select>
          </div> */}

          {/* Language - Hidden to match UI screenshot */}
          {/* <div className="AddStrategy__field">
            <label className="AddStrategy__label">Language</label>
            <select
              name="language"
              className="AddStrategy__select"
              value={formData.language}
              onChange={handleInputChange}
            >
              <option value="English">English</option>
              <option value="Spanish">Spanish</option>
              <option value="French">French</option>
              <option value="German">German</option>
              <option value="Italian">Italian</option>
              <option value="Portuguese">Portuguese</option>
              <option value="Chinese">Chinese</option>
              <option value="Japanese">Japanese</option>
              <option value="Korean">Korean</option>
              <option value="Other">Other</option>
            </select>
          </div> */}

          {/* Prerequisites - Hidden to match UI screenshot */}
          {/* <div className="AddStrategy__field">
            <label className="AddStrategy__label">Prerequisites</label>
            <div className="AddStrategy__array-field">
              <div className="AddStrategy__array-input">
                <input
                  type="text"
                  className="AddStrategy__input"
                  placeholder="Add a prerequisite..."
                  value={newPrerequisite}
                  onChange={(e) => setNewPrerequisite(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addArrayItem('prerequisites', newPrerequisite, setNewPrerequisite))}
                />
                <button
                  type="button"
                  className="AddStrategy__add-btn"
                  onClick={() => addArrayItem('prerequisites', newPrerequisite, setNewPrerequisite)}
                >
                  <FiPlus />
                </button>
              </div>
              <div className="AddStrategy__array-items">
                {formData.prerequisites.map((item, index) => (
                  <div key={index} className="AddStrategy__array-item">
                    <span>{item}</span>
                    <button
                      type="button"
                      className="AddStrategy__remove-btn"
                      onClick={() => removeArrayItem('prerequisites', index)}
                    >
                      <FiX />
                    </button>
                  </div>
                ))}
              </div>
            </div>
          </div> */}

          {/* Learning Objectives - Hidden to match UI screenshot */}
          {/* <div className="AddStrategy__field">
            <label className="AddStrategy__label">Learning Objectives</label>
            <div className="AddStrategy__array-field">
              <div className="AddStrategy__array-input">
                <input
                  type="text"
                  className="AddStrategy__input"
                  placeholder="Add a learning objective..."
                  value={newLearningObjective}
                  onChange={(e) => setNewLearningObjective(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addArrayItem('learningObjectives', newLearningObjective, setNewLearningObjective))}
                />
                <button
                  type="button"
                  className="AddStrategy__add-btn"
                  onClick={() => addArrayItem('learningObjectives', newLearningObjective, setNewLearningObjective)}
                >
                  <FiPlus />
                </button>
              </div>
              <div className="AddStrategy__array-items">
                {formData.learningObjectives.map((item, index) => (
                  <div key={index} className="AddStrategy__array-item">
                    <span>{item}</span>
                    <button
                      type="button"
                      className="AddStrategy__remove-btn"
                      onClick={() => removeArrayItem('learningObjectives', index)}
                    >
                      <FiX />
                    </button>
                  </div>
                ))}
              </div>
            </div>
          </div> */}

          {/* Equipment - Hidden to match UI screenshot */}
          {/* <div className="AddStrategy__field">
            <label className="AddStrategy__label">Required Equipment</label>
            <div className="AddStrategy__array-field">
              <div className="AddStrategy__array-input">
                <input
                  type="text"
                  className="AddStrategy__input"
                  placeholder="Add required equipment..."
                  value={newEquipment}
                  onChange={(e) => setNewEquipment(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addArrayItem('equipment', newEquipment, setNewEquipment))}
                />
                <button
                  type="button"
                  className="AddStrategy__add-btn"
                  onClick={() => addArrayItem('equipment', newEquipment, setNewEquipment)}
                >
                  <FiPlus />
                </button>
              </div>
              <div className="AddStrategy__array-items">
                {formData.equipment.map((item, index) => (
                  <div key={index} className="AddStrategy__array-item">
                    <span>{item}</span>
                    <button
                      type="button"
                      className="AddStrategy__remove-btn"
                      onClick={() => removeArrayItem('equipment', index)}
                    >
                      <FiX />
                    </button>
                  </div>
                ))}
              </div>
            </div>
          </div> */}

          {/* Video Length - Hidden to match UI screenshot */}
          {/* <div className="AddStrategy__field">
            <label className="AddStrategy__label">Video Length (minutes)</label>
            <input
              type="number"
              name="videoLength"
              className="AddStrategy__input"
              placeholder="Enter video length in minutes"
              value={formData.videoLength}
              onChange={handleInputChange}
              min="0"
            />
          </div> */}

          {/* Price - Hidden to match UI screenshot */}
          {/* <div className="AddStrategy__field">
            <label className="AddStrategy__label">Price ($)</label>
            <input
              type="number"
              name="price"
              className="AddStrategy__input"
              placeholder="Enter price"
              value={formData.price}
              onChange={handleInputChange}
              min="0"
              step="0.01"
            />
          </div> */}

          {/* Upload Section - This was moved up to match screenshot order */}

          {/* Error Display */}
          {isError && error && (
            <div className="AddStrategy__error">
              <p>Error: {error.message || 'Something went wrong'}</p>
            </div>
          )}

          {/* Action Buttons */}
          <div className="AddStrategy__actions">
            <button
              type="submit"
              className="btn btn-primary AddStrategy__submit-btn"
              disabled={isLoading}
            >
              {isLoading ? 'Creating...' : 'Add New Strategy'}
            </button>
            <button
              type="button"
              className="btn btn-outline AddStrategy__reset-btn"
              onClick={handleReset}
              disabled={isLoading}
            >
              Reset Form
            </button>
          </div>
        </form>
      </div>
    </SellerLayout>
  );
};

export default AddStrategy;
