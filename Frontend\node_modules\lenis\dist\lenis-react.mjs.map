{"version": 3, "sources": ["../packages/react/src/provider.tsx", "../packages/react/src/store.ts", "../packages/react/src/use-lenis.ts"], "sourcesContent": ["import Lenis, { type ScrollCallback } from 'lenis'\r\nimport {\r\n  createContext,\r\n  forwardRef,\r\n  useCallback,\r\n  useEffect,\r\n  useImperativeHandle,\r\n  useRef,\r\n  useState,\r\n} from 'react'\r\nimport { Store } from './store'\r\nimport type { LenisContextValue, LenisProps, LenisRef } from './types'\r\n\r\nexport const LenisContext = createContext<LenisContextValue | null>(null)\r\n\r\n/**\r\n * The root store for the lenis context\r\n *\r\n * This store serves as a fallback for the context if it is not available\r\n * and allows us to use the global lenis instance above a provider\r\n */\r\nexport const rootLenisContextStore = new Store<LenisContextValue | null>(null)\r\n\r\n/**\r\n * React component to setup a Lenis instance\r\n */\r\nexport const ReactLenis = forwardRef<LenisRef, LenisProps>(\r\n  (\r\n    {\r\n      children,\r\n      root = false,\r\n      options = {},\r\n      className,\r\n      autoRaf = true,\r\n      style,\r\n      props,\r\n    }: LenisProps,\r\n    ref\r\n  ) => {\r\n    const wrapperRef = useRef<HTMLDivElement | null>(null)\r\n    const contentRef = useRef<HTMLDivElement | null>(null)\r\n\r\n    const [lenis, setLenis] = useState<Lenis | undefined>(undefined)\r\n\r\n    // Setup ref\r\n    useImperativeHandle(\r\n      ref,\r\n      () => ({\r\n        wrapper: wrapperRef.current,\r\n        content: contentRef.current,\r\n        lenis,\r\n      }),\r\n      [lenis]\r\n    )\r\n\r\n    // Setup lenis instance\r\n    useEffect(() => {\r\n      const lenis = new Lenis({\r\n        ...options,\r\n        ...(!root && {\r\n          wrapper: wrapperRef.current!,\r\n          content: contentRef.current!,\r\n        }),\r\n        autoRaf: options?.autoRaf ?? autoRaf, // this is to avoid breaking the autoRaf prop if it's still used (require breaking change)\r\n      })\r\n\r\n      setLenis(lenis)\r\n\r\n      return () => {\r\n        lenis.destroy()\r\n        setLenis(undefined)\r\n      }\r\n    }, [root, JSON.stringify(options)])\r\n\r\n    // Handle callbacks\r\n    const callbacksRefs = useRef<\r\n      {\r\n        callback: ScrollCallback\r\n        priority: number\r\n      }[]\r\n    >([])\r\n\r\n    const addCallback: LenisContextValue['addCallback'] = useCallback(\r\n      (callback, priority) => {\r\n        callbacksRefs.current.push({ callback, priority })\r\n        callbacksRefs.current.sort((a, b) => a.priority - b.priority)\r\n      },\r\n      []\r\n    )\r\n\r\n    const removeCallback: LenisContextValue['removeCallback'] = useCallback(\r\n      (callback) => {\r\n        callbacksRefs.current = callbacksRefs.current.filter(\r\n          (cb) => cb.callback !== callback\r\n        )\r\n      },\r\n      []\r\n    )\r\n\r\n    // This makes sure to set the global context if the root is true\r\n    useEffect(() => {\r\n      if (root && lenis) {\r\n        rootLenisContextStore.set({ lenis, addCallback, removeCallback })\r\n\r\n        return () => rootLenisContextStore.set(null)\r\n      }\r\n    }, [root, lenis, addCallback, removeCallback])\r\n\r\n    // Setup callback listeners\r\n    useEffect(() => {\r\n      if (!lenis) return\r\n\r\n      const onScroll: ScrollCallback = (data) => {\r\n        for (let i = 0; i < callbacksRefs.current.length; i++) {\r\n          callbacksRefs.current[i]?.callback(data)\r\n        }\r\n      }\r\n\r\n      lenis.on('scroll', onScroll)\r\n\r\n      return () => {\r\n        lenis.off('scroll', onScroll)\r\n      }\r\n    }, [lenis])\r\n\r\n    return (\r\n      <LenisContext.Provider\r\n        value={{ lenis: lenis!, addCallback, removeCallback }}\r\n      >\r\n        {root ? (\r\n          children\r\n        ) : (\r\n          <div ref={wrapperRef} className={className} style={style} {...props}>\r\n            <div ref={contentRef}>{children}</div>\r\n          </div>\r\n        )}\r\n      </LenisContext.Provider>\r\n    )\r\n  }\r\n)\r\n", "import { useEffect, useState } from 'react'\n\ntype Listener<S> = (state: S) => void\n\nexport class Store<S> {\n  private listeners: Listener<S>[] = []\n\n  constructor(private state: S) {}\n\n  set(state: S) {\n    this.state = state\n\n    for (let listener of this.listeners) {\n      listener(this.state)\n    }\n  }\n\n  subscribe(listener: Listener<S>) {\n    this.listeners = [...this.listeners, listener]\n    return () => {\n      this.listeners = this.listeners.filter((l) => l !== listener)\n    }\n  }\n\n  get() {\n    return this.state\n  }\n}\n\nexport function useStore<S>(store: Store<S>) {\n  const [state, setState] = useState(store.get())\n\n  useEffect(() => {\n    return store.subscribe((state) => setState(state))\n  }, [store])\n\n  return state\n}\n", "import type { ScrollCallback } from 'lenis'\nimport { useContext, useEffect } from 'react'\nimport { LenisContext, rootLenisContextStore } from './provider'\nimport { useStore } from './store'\nimport type { LenisContextValue } from './types'\n\n// Fall back to an empty object if both context and store are not available\nconst fallbackContext: Partial<LenisContextValue> = {}\n\n/**\n * Hook to access the Lenis instance and its methods\n *\n * @example <caption>Scroll callback</caption>\n *          useLenis((lenis) => {\n *            if (lenis.isScrolling) {\n *              console.log('Scrolling...')\n *            }\n *\n *            if (lenis.progress === 1) {\n *              console.log('At the end!')\n *            }\n *          })\n *\n * @example <caption>Scroll callback with dependencies</caption>\n *          useLenis((lenis) => {\n *            if (lenis.isScrolling) {\n *              console.log('Scrolling...', someDependency)\n *            }\n *          }, [someDependency])\n * @example <caption>Scroll callback with priority</caption>\n *          useLenis((lenis) => {\n *            if (lenis.isScrolling) {\n *              console.log('Scrolling...')\n *            }\n *          }, [], 1)\n * @example <caption>Instance access</caption>\n *          const lenis = useLenis()\n *\n *          handleClick() {\n *            lenis.scrollTo(100, {\n *              lerp: 0.1,\n *              duration: 1,\n *              easing: (t) => t,\n *              onComplete: () => {\n *                console.log('Complete!')\n *              }\n *            })\n *          }\n */\nexport function useLenis(\n  callback?: ScrollCallback,\n  deps: any[] = [],\n  priority = 0\n) {\n  // Try to get the lenis instance from the context first\n  const localContext = useContext(LenisContext)\n  // Fall back to the root store if the context is not available\n  const rootContext = useStore(rootLenisContextStore)\n  // Fall back to the fallback context if all else fails\n  const currentContext = localContext ?? rootContext ?? fallbackContext\n\n  const { lenis, addCallback, removeCallback } = currentContext\n\n  useEffect(() => {\n    if (!callback || !addCallback || !removeCallback || !lenis) return\n\n    addCallback(callback, priority)\n    callback(lenis)\n\n    return () => {\n      removeCallback(callback)\n    }\n  }, [lenis, addCallback, removeCallback, priority, ...deps])\n\n  return lenis\n}\n"], "mappings": ";;;AAAA,OAAO,WAAoC;AAC3C;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA,aAAAA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAAC;AAAA,OACK;;;ACTP,SAAS,WAAW,gBAAgB;AAI7B,IAAM,QAAN,MAAe;AAAA,EAGpB,YAAoB,OAAU;AAAV;AAAA,EAAW;AAAA,EAFvB,YAA2B,CAAC;AAAA,EAIpC,IAAI,OAAU;AACZ,SAAK,QAAQ;AAEb,aAAS,YAAY,KAAK,WAAW;AACnC,eAAS,KAAK,KAAK;AAAA,IACrB;AAAA,EACF;AAAA,EAEA,UAAU,UAAuB;AAC/B,SAAK,YAAY,CAAC,GAAG,KAAK,WAAW,QAAQ;AAC7C,WAAO,MAAM;AACX,WAAK,YAAY,KAAK,UAAU,OAAO,CAAC,MAAM,MAAM,QAAQ;AAAA,IAC9D;AAAA,EACF;AAAA,EAEA,MAAM;AACJ,WAAO,KAAK;AAAA,EACd;AACF;AAEO,SAAS,SAAY,OAAiB;AAC3C,QAAM,CAAC,OAAO,QAAQ,IAAI,SAAS,MAAM,IAAI,CAAC;AAE9C,YAAU,MAAM;AACd,WAAO,MAAM,UAAU,CAACC,WAAU,SAASA,MAAK,CAAC;AAAA,EACnD,GAAG,CAAC,KAAK,CAAC;AAEV,SAAO;AACT;;;ADgGY;AAxHL,IAAM,eAAe,cAAwC,IAAI;AAQjE,IAAM,wBAAwB,IAAI,MAAgC,IAAI;AAKtE,IAAM,aAAa;AAAA,EACxB,CACE;AAAA,IACE;AAAA,IACA,OAAO;AAAA,IACP,UAAU,CAAC;AAAA,IACX;AAAA,IACA,UAAU;AAAA,IACV;AAAA,IACA;AAAA,EACF,GACA,QACG;AACH,UAAM,aAAa,OAA8B,IAAI;AACrD,UAAM,aAAa,OAA8B,IAAI;AAErD,UAAM,CAAC,OAAO,QAAQ,IAAIC,UAA4B,MAAS;AAG/D;AAAA,MACE;AAAA,MACA,OAAO;AAAA,QACL,SAAS,WAAW;AAAA,QACpB,SAAS,WAAW;AAAA,QACpB;AAAA,MACF;AAAA,MACA,CAAC,KAAK;AAAA,IACR;AAGA,IAAAC,WAAU,MAAM;AACd,YAAMC,SAAQ,IAAI,MAAM;AAAA,QACtB,GAAG;AAAA,QACH,GAAI,CAAC,QAAQ;AAAA,UACX,SAAS,WAAW;AAAA,UACpB,SAAS,WAAW;AAAA,QACtB;AAAA,QACA,SAAS,SAAS,WAAW;AAAA;AAAA,MAC/B,CAAC;AAED,eAASA,MAAK;AAEd,aAAO,MAAM;AACX,QAAAA,OAAM,QAAQ;AACd,iBAAS,MAAS;AAAA,MACpB;AAAA,IACF,GAAG,CAAC,MAAM,KAAK,UAAU,OAAO,CAAC,CAAC;AAGlC,UAAM,gBAAgB,OAKpB,CAAC,CAAC;AAEJ,UAAM,cAAgD;AAAA,MACpD,CAAC,UAAU,aAAa;AACtB,sBAAc,QAAQ,KAAK,EAAE,UAAU,SAAS,CAAC;AACjD,sBAAc,QAAQ,KAAK,CAAC,GAAG,MAAM,EAAE,WAAW,EAAE,QAAQ;AAAA,MAC9D;AAAA,MACA,CAAC;AAAA,IACH;AAEA,UAAM,iBAAsD;AAAA,MAC1D,CAAC,aAAa;AACZ,sBAAc,UAAU,cAAc,QAAQ;AAAA,UAC5C,CAAC,OAAO,GAAG,aAAa;AAAA,QAC1B;AAAA,MACF;AAAA,MACA,CAAC;AAAA,IACH;AAGA,IAAAD,WAAU,MAAM;AACd,UAAI,QAAQ,OAAO;AACjB,8BAAsB,IAAI,EAAE,OAAO,aAAa,eAAe,CAAC;AAEhE,eAAO,MAAM,sBAAsB,IAAI,IAAI;AAAA,MAC7C;AAAA,IACF,GAAG,CAAC,MAAM,OAAO,aAAa,cAAc,CAAC;AAG7C,IAAAA,WAAU,MAAM;AACd,UAAI,CAAC,MAAO;AAEZ,YAAM,WAA2B,CAAC,SAAS;AACzC,iBAAS,IAAI,GAAG,IAAI,cAAc,QAAQ,QAAQ,KAAK;AACrD,wBAAc,QAAQ,CAAC,GAAG,SAAS,IAAI;AAAA,QACzC;AAAA,MACF;AAEA,YAAM,GAAG,UAAU,QAAQ;AAE3B,aAAO,MAAM;AACX,cAAM,IAAI,UAAU,QAAQ;AAAA,MAC9B;AAAA,IACF,GAAG,CAAC,KAAK,CAAC;AAEV,WACE;AAAA,MAAC,aAAa;AAAA,MAAb;AAAA,QACC,OAAO,EAAE,OAAe,aAAa,eAAe;AAAA,QAEnD,iBACC,WAEA,oBAAC,SAAI,KAAK,YAAY,WAAsB,OAAe,GAAG,OAC5D,8BAAC,SAAI,KAAK,YAAa,UAAS,GAClC;AAAA;AAAA,IAEJ;AAAA,EAEJ;AACF;;;AE1IA,SAAS,YAAY,aAAAE,kBAAiB;AAMtC,IAAM,kBAA8C,CAAC;AA0C9C,SAAS,SACd,UACA,OAAc,CAAC,GACf,WAAW,GACX;AAEA,QAAM,eAAe,WAAW,YAAY;AAE5C,QAAM,cAAc,SAAS,qBAAqB;AAElD,QAAM,iBAAiB,gBAAgB,eAAe;AAEtD,QAAM,EAAE,OAAO,aAAa,eAAe,IAAI;AAE/C,EAAAC,WAAU,MAAM;AACd,QAAI,CAAC,YAAY,CAAC,eAAe,CAAC,kBAAkB,CAAC,MAAO;AAE5D,gBAAY,UAAU,QAAQ;AAC9B,aAAS,KAAK;AAEd,WAAO,MAAM;AACX,qBAAe,QAAQ;AAAA,IACzB;AAAA,EACF,GAAG,CAAC,OAAO,aAAa,gBAAgB,UAAU,GAAG,IAAI,CAAC;AAE1D,SAAO;AACT;", "names": ["useEffect", "useState", "state", "useState", "useEffect", "lenis", "useEffect", "useEffect"]}