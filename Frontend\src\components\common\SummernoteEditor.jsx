import React, { useState, useRef } from 'react';
import { FiBold, FiItalic, FiUnderline, FiList, FiLink } from 'react-icons/fi';

const SummernoteEditor = ({
  value = '',
  onChange,
  placeholder = 'Enter text here...',
  height = 200,
  className = '',
  disabled = false
}) => {
  const textareaRef = useRef(null);
  const [isFocused, setIsFocused] = useState(false);

  const handleChange = (e) => {
    if (onChange) {
      onChange(e.target.value);
    }
  };

  const insertText = (before, after = '') => {
    const textarea = textareaRef.current;
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = value.substring(start, end);
    const newText = value.substring(0, start) + before + selectedText + after + value.substring(end);

    if (onChange) {
      onChange(newText);
    }

    // Restore focus and cursor position
    setTimeout(() => {
      textarea.focus();
      textarea.setSelectionRange(start + before.length, end + before.length);
    }, 0);
  };

  const formatBold = () => insertText('**', '**');
  const formatItalic = () => insertText('*', '*');
  const formatUnderline = () => insertText('<u>', '</u>');
  const formatList = () => insertText('\n• ');
  const formatLink = () => insertText('[', '](url)');

  return (
    <div className={`rich-text-editor ${className} ${isFocused ? 'focused' : ''}`}>
      {/* Toolbar */}
      <div className="rich-text-toolbar">
        <button
          type="button"
          className="toolbar-btn"
          onClick={formatBold}
          title="Bold"
          disabled={disabled}
        >
          <FiBold />
        </button>
        <button
          type="button"
          className="toolbar-btn"
          onClick={formatItalic}
          title="Italic"
          disabled={disabled}
        >
          <FiItalic />
        </button>
        <button
          type="button"
          className="toolbar-btn"
          onClick={formatUnderline}
          title="Underline"
          disabled={disabled}
        >
          <FiUnderline />
        </button>
        <button
          type="button"
          className="toolbar-btn"
          onClick={formatList}
          title="Bullet List"
          disabled={disabled}
        >
          <FiList />
        </button>
        <button
          type="button"
          className="toolbar-btn"
          onClick={formatLink}
          title="Insert Link"
          disabled={disabled}
        >
          <FiLink />
        </button>
      </div>

      {/* Text Area */}
      <textarea
        ref={textareaRef}
        value={value}
        onChange={handleChange}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
        placeholder={placeholder}
        disabled={disabled}
        className="rich-text-area"
        style={{ height: `${height}px` }}
      />
    </div>
  );
};

export default SummernoteEditor;
