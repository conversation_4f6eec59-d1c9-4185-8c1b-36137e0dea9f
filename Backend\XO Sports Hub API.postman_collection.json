{"info": {"_postman_id": "e3c9bf42-21de-451e-8699-77187f464c8c", "name": "XO Sports Hub API", "description": "A comprehensive API collection for the XO Sports Hub platform - a digital content marketplace for sports training.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "42589064", "_collection_link": "https://physiosportsclub.postman.co/workspace/Team-Workspace~89fdcb5f-4dbe-430d-86af-53316c49fb4e/collection/42589064-e3c9bf42-21de-451e-8699-77187f464c8c?action=share&source=collection_link&creator=42589064"}, "item": [{"name": "Authentication", "item": [{"name": "Register User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"firstName\": \"<PERSON>\",\n    \"lastName\": \"<PERSON>e\",\n    \"email\": \"<EMAIL>\",\n    \"mobile\": \"+1234567890\",\n    \"role\": \"buyer\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/register", "host": ["{{baseUrl}}"], "path": ["api", "auth", "register"]}, "description": "Register a new user with the platform. No password is required as the system uses OTP-based authentication.\n\nResponse includes a userId that should be used in the verify-otp endpoint."}, "response": []}, {"name": "Login User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\"\n}\n\n// OR use mobile\n// {\n//     \"mobile\": \"+1234567890\"\n// }"}, "url": {"raw": "{{baseUrl}}/api/auth/send-otp", "host": ["{{baseUrl}}"], "path": ["api", "auth", "send-otp"]}, "description": "Send an OTP (One-Time Password) to the user's email and mobile for authentication. Provide either email or mobile to identify the user.\n\nResponse includes a userId that should be used in the verify-otp endpoint."}, "response": []}, {"name": "Verify OTP", "event": [{"listen": "test", "script": {"exec": ["var jsonData = pm.response.json();", "if (jsonData.token) {", "    pm.environment.set(\"authToken\", jsonData.token);", "    if (jsonData.data && jsonData.data.user) {", "        pm.environment.set(\"userId\", jsonData.data.user._id);", "    }", "}"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"userId\": \"{{userId}}\",\n    \"otp\": \"471033\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/verify-otp", "host": ["{{baseUrl}}"], "path": ["api", "auth", "verify-otp"]}, "description": "Verify the OTP sent to the user and authenticate them. The userId comes from the register or send-otp response.\n\nResponse includes a JWT token that should be used for authenticated requests."}, "response": []}, {"name": "Get Current User", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/auth/me", "host": ["{{baseUrl}}"], "path": ["api", "auth", "me"]}, "description": "Get the currently authenticated user's profile information."}, "response": []}, {"name": "Logout", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/auth/logout", "host": ["{{baseUrl}}"], "path": ["api", "auth", "logout"]}, "description": "Logout the currently authenticated user and clear their session."}, "response": []}, {"name": "<PERSON><PERSON><PERSON>", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/auth/verify-email/:token", "host": ["{{baseUrl}}"], "path": ["api", "auth", "verify-email", ":token"], "variable": [{"key": "token", "value": "email_verification_token", "description": "Email verification token"}]}, "description": "Verify a user's email address using the token sent to their email."}, "response": []}], "description": "Endpoints for user authentication including registration, OTP verification, and session management."}, {"name": "User Management", "item": [{"name": "Get All Users (Admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/users", "host": ["{{baseUrl}}"], "path": ["api", "users"]}, "description": "Get a list of all users. Admin access only."}, "response": []}, {"name": "Get Single User (Admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/users/:id", "host": ["{{baseUrl}}"], "path": ["api", "users", ":id"], "variable": [{"key": "id", "value": "{{userId}}", "description": "User ID"}]}, "description": "Get details of a specific user by ID. Admin access only."}, "response": []}, {"name": "Create User (Admin)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"firstName\": \"Admin\",\n    \"lastName\": \"User\",\n    \"email\": \"<EMAIL>\",\n    \"mobile\": \"+1987654321\",\n    \"role\": \"admin\"\n}"}, "url": {"raw": "{{baseUrl}}/api/users", "host": ["{{baseUrl}}"], "path": ["api", "users"]}, "description": "Create a new user. Admin access only."}, "response": []}, {"name": "Update User (Admin)", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"firstName\": \"Updated\",\n    \"lastName\": \"User\",\n    \"email\": \"<EMAIL>\",\n    \"mobile\": \"+1987654321\",\n    \"role\": \"buyer\"\n}"}, "url": {"raw": "{{baseUrl}}/api/users/:id", "host": ["{{baseUrl}}"], "path": ["api", "users", ":id"], "variable": [{"key": "id", "value": "{{userId}}", "description": "User ID"}]}, "description": "Update a user's information. Admin access only."}, "response": []}, {"name": "Delete User (Admin)", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/users/:id", "host": ["{{baseUrl}}"], "path": ["api", "users", ":id"], "variable": [{"key": "id", "value": "{{userId}}", "description": "User ID"}]}, "description": "Delete a user. Admin access only."}, "response": []}], "description": "Endpoints for managing users, including admin operations and user profile management."}, {"name": "Content Management", "item": [{"name": "Get All Content", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/content", "host": ["{{baseUrl}}"], "path": ["api", "content"]}, "description": "Get a list of all available content. Public access."}, "response": []}, {"name": "Get Content Categories", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/content/categories", "host": ["{{baseUrl}}"], "path": ["api", "content", "categories"]}, "description": "Get a list of all content categories. Public access."}, "response": []}, {"name": "Get Trending Content", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/content/trending", "host": ["{{baseUrl}}"], "path": ["api", "content", "trending"]}, "description": "Get a list of trending content. Public access."}, "response": []}, {"name": "Get Single Content", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/content/:id", "host": ["{{baseUrl}}"], "path": ["api", "content", ":id"], "variable": [{"key": "id", "value": "{{contentId}}", "description": "Content ID"}]}, "description": "Get details of a specific content by ID. Public access."}, "response": []}, {"name": "Create Content (Seller)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"title\": \"Advanced Basketball Techniques\",\n    \"description\": \"Learn professional basketball techniques from a former college coach.\",\n    \"sport\": \"Basketball\",\n    \"contentType\": \"Video\",\n    \"fileUrl\": \"https://example.com/videos/basketball.mp4\",\n    \"previewUrl\": \"https://example.com/videos/basketball-preview.mp4\",\n    \"thumbnailUrl\": \"https://example.com/images/basketball-thumb.jpg\",\n    \"duration\": 45,\n    \"fileSize\": 250000000,\n    \"tags\": [\"basketball\", \"technique\", \"training\"],\n    \"difficulty\": \"Intermediate\",\n    \"saleType\": \"Fixed\",\n    \"price\": 29.99,\n    \"allowCustomRequests\": true\n}"}, "url": {"raw": "{{baseUrl}}/api/content", "host": ["{{baseUrl}}"], "path": ["api", "content"]}, "description": "Create a new content item. Seller access only."}, "response": []}, {"name": "Update Content (Seller)", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"title\": \"Updated Basketball Techniques\",\n    \"description\": \"Updated description for basketball techniques.\",\n    \"price\": 39.99\n}"}, "url": {"raw": "{{baseUrl}}/api/content/:id", "host": ["{{baseUrl}}"], "path": ["api", "content", ":id"], "variable": [{"key": "id", "value": "{{contentId}}", "description": "Content ID"}]}, "description": "Update a content item. Seller access only (must be the owner of the content)."}, "response": []}, {"name": "Delete Content (Seller)", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/content/:id", "host": ["{{baseUrl}}"], "path": ["api", "content", ":id"], "variable": [{"key": "id", "value": "{{contentId}}", "description": "Content ID"}]}, "description": "Delete a content item. Seller access only (must be the owner of the content)."}, "response": []}, {"name": "Get Seller Content", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/content/seller/me", "host": ["{{baseUrl}}"], "path": ["api", "content", "seller", "me"]}, "description": "Get a list of content items created by the authenticated seller. Seller access only."}, "response": []}, {"name": "Upload Content File", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": "/path/to/your/file.mp4"}, {"key": "type", "value": "content", "type": "text"}]}, "url": {"raw": "{{baseUrl}}/api/content/upload", "host": ["{{baseUrl}}"], "path": ["api", "content", "upload"]}, "description": "Upload a content file. Seller access only."}, "response": []}], "description": "Endpoints for managing content, including listing, creating, updating, and deleting content."}, {"name": "Order Management", "item": [{"name": "Get All Orders (Admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/orders", "host": ["{{baseUrl}}"], "path": ["api", "orders"]}, "description": "Get a list of all orders. Admin access only."}, "response": []}, {"name": "Get Single Order", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/orders/:id", "host": ["{{baseUrl}}"], "path": ["api", "orders", ":id"], "variable": [{"key": "id", "value": "{{orderId}}", "description": "Order ID"}]}, "description": "Get details of a specific order by ID. Access restricted to the buyer, seller, or admin."}, "response": []}, {"name": "Create Order (Buyer)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"contentId\": \"{{contentId}}\",\n    \"orderType\": \"Fixed\"\n}"}, "url": {"raw": "{{baseUrl}}/api/orders", "host": ["{{baseUrl}}"], "path": ["api", "orders"]}, "description": "Create a new order for content. Buyer access only."}, "response": []}, {"name": "Update Order Status (Admin)", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"status\": \"Completed\"\n}"}, "url": {"raw": "{{baseUrl}}/api/orders/:id", "host": ["{{baseUrl}}"], "path": ["api", "orders", ":id"], "variable": [{"key": "id", "value": "{{orderId}}", "description": "Order ID"}]}, "description": "Update the status of an order. Admin access only."}, "response": []}, {"name": "Get Buyer Orders", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/orders/buyer", "host": ["{{baseUrl}}"], "path": ["api", "orders", "buyer"]}, "description": "Get a list of orders placed by the authenticated buyer. Buyer access only."}, "response": []}, {"name": "Get Seller Orders", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/orders/seller", "host": ["{{baseUrl}}"], "path": ["api", "orders", "seller"]}, "description": "Get a list of orders for content sold by the authenticated seller. Seller access only."}, "response": []}, {"name": "Download Content (Buyer)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/orders/:id/download", "host": ["{{baseUrl}}"], "path": ["api", "orders", ":id", "download"], "variable": [{"key": "id", "value": "{{orderId}}", "description": "Order ID"}]}, "description": "Download the content associated with an order. Buyer access only (must be the buyer of the order)."}, "response": []}], "description": "Endpoints for managing orders, including creating, updating, and viewing orders."}, {"name": "Bid Management", "item": [{"name": "Get All Bids (Admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/bids", "host": ["{{baseUrl}}"], "path": ["api", "bids"]}, "description": "Get a list of all bids. Admin access only."}, "response": []}, {"name": "Get Single Bid", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/bids/:id", "host": ["{{baseUrl}}"], "path": ["api", "bids", ":id"], "variable": [{"key": "id", "value": "{{bidId}}", "description": "Bid ID"}]}, "description": "Get details of a specific bid by ID. Access restricted to the bidder, content seller, or admin."}, "response": []}, {"name": "Create Bid (Buyer)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"contentId\": \"{{contentId}}\",\n    \"amount\": 50.00\n}"}, "url": {"raw": "{{baseUrl}}/api/bids", "host": ["{{baseUrl}}"], "path": ["api", "bids"]}, "description": "Create a new bid for auction-based content. Buyer access only."}, "response": []}, {"name": "<PERSON><PERSON> (Buyer)", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/bids/:id/cancel", "host": ["{{baseUrl}}"], "path": ["api", "bids", ":id", "cancel"], "variable": [{"key": "id", "value": "{{bidId}}", "description": "Bid ID"}]}, "description": "Cancel a bid. Buyer access only (must be the bidder)."}, "response": []}, {"name": "Get Bids for Content", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/bids/content/:contentId", "host": ["{{baseUrl}}"], "path": ["api", "bids", "content", ":contentId"], "variable": [{"key": "contentId", "value": "{{contentId}}", "description": "Content ID"}]}, "description": "Get a list of bids for a specific content item. Public access."}, "response": []}, {"name": "Get User Bids", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/bids/user", "host": ["{{baseUrl}}"], "path": ["api", "bids", "user"]}, "description": "Get a list of bids placed by the authenticated user. Buyer access only."}, "response": []}, {"name": "End Auction (Seller)", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/bids/end-auction/:contentId", "host": ["{{baseUrl}}"], "path": ["api", "bids", "end-auction", ":contentId"], "variable": [{"key": "contentId", "value": "{{contentId}}", "description": "Content ID"}]}, "description": "End an auction for content. Seller access only (must be the content owner)."}, "response": []}], "description": "Endpoints for managing bids for auction-based content."}, {"name": "Custom Request Management", "item": [{"name": "Get All Requests (Admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/requests", "host": ["{{baseUrl}}"], "path": ["api", "requests"]}, "description": "Get a list of all custom requests. Admin access only."}, "response": []}, {"name": "Get Single Request", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/requests/:id", "host": ["{{baseUrl}}"], "path": ["api", "requests", ":id"], "variable": [{"key": "id", "value": "{{requestId}}", "description": "Request ID"}]}, "description": "Get details of a specific custom request by ID. Access restricted to the requester, seller, or admin."}, "response": []}, {"name": "Create Request (Buyer)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"sellerId\": \"{{sellerId}}\",\n    \"title\": \"Custom Basketball Training\",\n    \"description\": \"I need a custom training program for improving my jump shot.\",\n    \"sport\": \"Basketball\",\n    \"contentType\": \"Video\",\n    \"budget\": 100.00\n}"}, "url": {"raw": "{{baseUrl}}/api/requests", "host": ["{{baseUrl}}"], "path": ["api", "requests"]}, "description": "Create a new custom content request. Buyer access only."}, "response": []}, {"name": "Respond to Request (Seller)", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"status\": \"Accepted\",\n    \"price\": 120.00,\n    \"message\": \"I can create this custom content for you. It will take about 2 weeks.\",\n    \"deliveryDate\": \"2023-06-30T00:00:00.000Z\"\n}"}, "url": {"raw": "{{baseUrl}}/api/requests/:id/respond", "host": ["{{baseUrl}}"], "path": ["api", "requests", ":id", "respond"], "variable": [{"key": "id", "value": "{{requestId}}", "description": "Request ID"}]}, "description": "Respond to a custom content request. Seller access only (must be the request recipient)."}, "response": []}, {"name": "Cancel Request (Buyer)", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/requests/:id/cancel", "host": ["{{baseUrl}}"], "path": ["api", "requests", ":id", "cancel"], "variable": [{"key": "id", "value": "{{requestId}}", "description": "Request ID"}]}, "description": "Cancel a custom content request. Buyer access only (must be the requester)."}, "response": []}, {"name": "Submit Content for Request (Seller)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": "/path/to/your/file.mp4"}, {"key": "message", "value": "Here is the custom content you requested. Let me know if you need any adjustments.", "type": "text"}]}, "url": {"raw": "{{baseUrl}}/api/requests/:id/submit", "host": ["{{baseUrl}}"], "path": ["api", "requests", ":id", "submit"], "variable": [{"key": "id", "value": "{{requestId}}", "description": "Request ID"}]}, "description": "Submit content for a custom request. Seller access only (must be the request recipient)."}, "response": []}, {"name": "Get Buyer Requests", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/requests/buyer", "host": ["{{baseUrl}}"], "path": ["api", "requests", "buyer"]}, "description": "Get a list of custom requests created by the authenticated buyer. Buyer access only."}, "response": []}, {"name": "Get Seller Requests", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/requests/seller", "host": ["{{baseUrl}}"], "path": ["api", "requests", "seller"]}, "description": "Get a list of custom requests received by the authenticated seller. Seller access only."}, "response": []}], "description": "Endpoints for managing custom content requests from buyers to sellers."}, {"name": "Payment Management", "item": [{"name": "Get All Payments (Admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/payments", "host": ["{{baseUrl}}"], "path": ["api", "payments"]}, "description": "Get a list of all payments. Admin access only."}, "response": []}, {"name": "Get Single Payment", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/payments/:id", "host": ["{{baseUrl}}"], "path": ["api", "payments", ":id"], "variable": [{"key": "id", "value": "{{paymentId}}", "description": "Payment ID"}]}, "description": "Get details of a specific payment by ID. Access restricted to the payer, payee, or admin."}, "response": []}, {"name": "Create Payment Intent (Buyer)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"orderId\": \"{{orderId}}\"\n}"}, "url": {"raw": "{{baseUrl}}/api/payments/create-intent", "host": ["{{baseUrl}}"], "path": ["api", "payments", "create-intent"]}, "description": "Create a payment intent for an order. Buyer access only."}, "response": []}, {"name": "Confirm Payment (Buyer)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"paymentIntentId\": \"pi_123456789\",\n    \"orderId\": \"{{orderId}}\"\n}"}, "url": {"raw": "{{baseUrl}}/api/payments/confirm", "host": ["{{baseUrl}}"], "path": ["api", "payments", "confirm"]}, "description": "Confirm a payment for an order. Buyer access only."}, "response": []}, {"name": "Process Stripe Webhook", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Stripe-Signature", "value": "t=timestamp,v1=signature"}], "body": {"mode": "raw", "raw": "{\n    \"id\": \"evt_123456789\",\n    \"object\": \"event\",\n    \"type\": \"payment_intent.succeeded\",\n    \"data\": {\n        \"object\": {\n            \"id\": \"pi_123456789\",\n            \"object\": \"payment_intent\",\n            \"amount\": 2999,\n            \"currency\": \"usd\",\n            \"status\": \"succeeded\"\n        }\n    }\n}"}, "url": {"raw": "{{baseUrl}}/api/payments/webhook", "host": ["{{baseUrl}}"], "path": ["api", "payments", "webhook"]}, "description": "Process a Stripe webhook event. Public access (secured by Stripe signature)."}, "response": []}, {"name": "Get Buyer Payments", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/payments/buyer", "host": ["{{baseUrl}}"], "path": ["api", "payments", "buyer"]}, "description": "Get a list of payments made by the authenticated buyer. Buyer access only."}, "response": []}, {"name": "Get Seller Payments", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/payments/seller", "host": ["{{baseUrl}}"], "path": ["api", "payments", "seller"]}, "description": "Get a list of payments received by the authenticated seller. Seller access only."}, "response": []}, {"name": "Process Payout (Admin)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"amount\": 100.00,\n    \"sellerId\": \"{{sellerId}}\"\n}"}, "url": {"raw": "{{baseUrl}}/api/payments/:id/payout", "host": ["{{baseUrl}}"], "path": ["api", "payments", ":id", "payout"], "variable": [{"key": "id", "value": "{{paymentId}}", "description": "Payment ID"}]}, "description": "Process a payout to a seller. Admin access only."}, "response": []}], "description": "Endpoints for managing payments, including creating payment intents, confirming payments, and processing payouts."}, {"name": "Notification Management", "item": [{"name": "Get All Notifications (Admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/notifications", "host": ["{{baseUrl}}"], "path": ["api", "notifications"]}, "description": "Get a list of all notifications. Admin access only."}, "response": []}, {"name": "Get User Notifications", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/notifications/me", "host": ["{{baseUrl}}"], "path": ["api", "notifications", "me"]}, "description": "Get a list of notifications for the authenticated user."}, "response": []}, {"name": "Create Notification (Admin)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"user\": \"{{userId}}\",\n    \"title\": \"New Feature Available\",\n    \"message\": \"We've added a new feature to the platform. Check it out!\",\n    \"type\": \"system\"\n}"}, "url": {"raw": "{{baseUrl}}/api/notifications", "host": ["{{baseUrl}}"], "path": ["api", "notifications"]}, "description": "Create a new notification for a user. Admin access only."}, "response": []}, {"name": "Mark Notification as Read", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/notifications/:id/read", "host": ["{{baseUrl}}"], "path": ["api", "notifications", ":id", "read"], "variable": [{"key": "id", "value": "{{notificationId}}", "description": "Notification ID"}]}, "description": "Mark a notification as read. User access only (must be the notification recipient)."}, "response": []}, {"name": "Mark All Notifications as Read", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/notifications/read-all", "host": ["{{baseUrl}}"], "path": ["api", "notifications", "read-all"]}, "description": "Mark all notifications as read for the authenticated user."}, "response": []}, {"name": "Delete Notification", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/notifications/:id", "host": ["{{baseUrl}}"], "path": ["api", "notifications", ":id"], "variable": [{"key": "id", "value": "{{notificationId}}", "description": "Notification ID"}]}, "description": "Delete a notification. User access only (must be the notification recipient)."}, "response": []}, {"name": "Get Unread Notification Count", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/notifications/unread-count", "host": ["{{baseUrl}}"], "path": ["api", "notifications", "unread-count"]}, "description": "Get the count of unread notifications for the authenticated user."}, "response": []}], "description": "Endpoints for managing notifications, including creating, reading, and deleting notifications."}, {"name": "CMS Management", "item": [{"name": "Get All CMS Pages (Admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/cms", "host": ["{{baseUrl}}"], "path": ["api", "cms"]}, "description": "Get a list of all CMS pages. Admin access only."}, "response": []}, {"name": "Get Published CMS Pages", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/cms/published", "host": ["{{baseUrl}}"], "path": ["api", "cms", "published"]}, "description": "Get a list of published CMS pages. Public access."}, "response": []}, {"name": "Get Single CMS Page", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/cms/:slug", "host": ["{{baseUrl}}"], "path": ["api", "cms", ":slug"], "variable": [{"key": "slug", "value": "about-us", "description": "CMS page slug"}]}, "description": "Get a specific CMS page by slug. Public access."}, "response": []}, {"name": "Create CMS Page (Admin)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"title\": \"Terms of Service\",\n    \"content\": \"<h1>Terms of Service</h1><p>These are the terms of service for XO Sports Hub.</p>\",\n    \"status\": \"Published\",\n    \"metaTitle\": \"Terms of Service - XO Sports Hub\",\n    \"metaDescription\": \"Terms of service for the XO Sports Hub platform.\"\n}"}, "url": {"raw": "{{baseUrl}}/api/cms", "host": ["{{baseUrl}}"], "path": ["api", "cms"]}, "description": "Create a new CMS page. Admin access only."}, "response": []}, {"name": "Update CMS Page (Admin)", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"title\": \"Updated Terms of Service\",\n    \"content\": \"<h1>Updated Terms of Service</h1><p>These are the updated terms of service for XO Sports Hub.</p>\",\n    \"status\": \"Published\"\n}"}, "url": {"raw": "{{baseUrl}}/api/cms/:id", "host": ["{{baseUrl}}"], "path": ["api", "cms", ":id"], "variable": [{"key": "id", "value": "{{cmsPageId}}", "description": "CMS page ID"}]}, "description": "Update a CMS page. Admin access only."}, "response": []}, {"name": "Delete CMS Page (Admin)", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/cms/:id", "host": ["{{baseUrl}}"], "path": ["api", "cms", ":id"], "variable": [{"key": "id", "value": "{{cmsPageId}}", "description": "CMS page ID"}]}, "description": "Delete a CMS page. Admin access only."}, "response": []}, {"name": "Submit Contact Form", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"<PERSON>\",\n    \"email\": \"<EMAIL>\",\n    \"subject\": \"General Inquiry\",\n    \"message\": \"I have a question about your platform.\"\n}"}, "url": {"raw": "{{baseUrl}}/api/cms/contact", "host": ["{{baseUrl}}"], "path": ["api", "cms", "contact"]}, "description": "Submit a contact form. Public access."}, "response": []}], "description": "Endpoints for managing CMS pages and contact form submissions."}, {"name": "Settings Management", "item": [{"name": "Get All Settings (Admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/settings", "host": ["{{baseUrl}}"], "path": ["api", "settings"]}, "description": "Get a list of all platform settings. Admin access only."}, "response": []}, {"name": "Get Public Settings", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/settings/public", "host": ["{{baseUrl}}"], "path": ["api", "settings", "public"]}, "description": "Get a list of public platform settings. Public access."}, "response": []}, {"name": "Get Single Setting (Admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/settings/:id", "host": ["{{baseUrl}}"], "path": ["api", "settings", ":id"], "variable": [{"key": "id", "value": "{{settingId}}", "description": "Setting ID"}]}, "description": "Get a specific setting by ID. Admin access only."}, "response": []}, {"name": "Create Setting (Admin)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"key\": \"platform_fee\",\n    \"value\": 10,\n    \"group\": \"payment\",\n    \"isPublic\": true,\n    \"description\": \"Platform fee percentage\"\n}"}, "url": {"raw": "{{baseUrl}}/api/settings", "host": ["{{baseUrl}}"], "path": ["api", "settings"]}, "description": "Create a new platform setting. Admin access only."}, "response": []}, {"name": "Update Setting (Admin)", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"value\": 12,\n    \"isPublic\": true,\n    \"description\": \"Updated platform fee percentage\"\n}"}, "url": {"raw": "{{baseUrl}}/api/settings/:id", "host": ["{{baseUrl}}"], "path": ["api", "settings", ":id"], "variable": [{"key": "id", "value": "{{settingId}}", "description": "Setting ID"}]}, "description": "Update a platform setting. Admin access only."}, "response": []}, {"name": "Delete Setting (Admin)", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/settings/:id", "host": ["{{baseUrl}}"], "path": ["api", "settings", ":id"], "variable": [{"key": "id", "value": "{{settingId}}", "description": "Setting ID"}]}, "description": "Delete a platform setting. Admin access only."}, "response": []}, {"name": "Update Multiple Settings (Admin)", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"settings\": [\n        {\n            \"key\": \"platform_fee\",\n            \"value\": 12\n        },\n        {\n            \"key\": \"support_email\",\n            \"value\": \"<EMAIL>\"\n        }\n    ]\n}"}, "url": {"raw": "{{baseUrl}}/api/settings", "host": ["{{baseUrl}}"], "path": ["api", "settings"]}, "description": "Update multiple platform settings at once. Admin access only."}, "response": []}], "description": "Endpoints for managing platform settings."}, {"name": "Dashboard", "item": [{"name": "Get Dashboard Stats (Admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/dashboard/stats", "host": ["{{baseUrl}}"], "path": ["api", "dashboard", "stats"]}, "description": "Get overall dashboard statistics. Admin access only."}, "response": []}, {"name": "Get User Stats (Admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/dashboard/users", "host": ["{{baseUrl}}"], "path": ["api", "dashboard", "users"]}, "description": "Get user-related statistics. Admin access only."}, "response": []}, {"name": "Get Content Stats (Admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/dashboard/content", "host": ["{{baseUrl}}"], "path": ["api", "dashboard", "content"]}, "description": "Get content-related statistics. Admin access only."}, "response": []}, {"name": "Get Order Stats (Admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/dashboard/orders", "host": ["{{baseUrl}}"], "path": ["api", "dashboard", "orders"]}, "description": "Get order-related statistics. Admin access only."}, "response": []}, {"name": "Get Revenue Stats (Admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/dashboard/revenue", "host": ["{{baseUrl}}"], "path": ["api", "dashboard", "revenue"]}, "description": "Get revenue-related statistics. Admin access only."}, "response": []}, {"name": "Get Recent Activity (Admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/dashboard/activity", "host": ["{{baseUrl}}"], "path": ["api", "dashboard", "activity"]}, "description": "Get recent platform activity. Admin access only."}, "response": []}], "description": "Endpoints for retrieving dashboard statistics and data."}, {"name": "Reviews Management", "item": [{"name": "Get All Reviews (Admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/reviews", "host": ["{{baseUrl}}"], "path": ["api", "reviews"]}, "description": "Get a list of all reviews. Admin access only."}, "response": []}, {"name": "Get Single Review", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/reviews/:id", "host": ["{{baseUrl}}"], "path": ["api", "reviews", ":id"], "variable": [{"key": "id", "value": "{{reviewId}}", "description": "Review ID"}]}, "description": "Get details of a specific review by ID. Public access."}, "response": []}, {"name": "Get Reviews for Content", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/reviews/content/:contentId", "host": ["{{baseUrl}}"], "path": ["api", "reviews", "content", ":contentId"], "variable": [{"key": "contentId", "value": "{{contentId}}", "description": "Content ID"}]}, "description": "Get a list of reviews for a specific content item. Public access."}, "response": []}, {"name": "Get Reviews for Seller", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/reviews/seller/:sellerId", "host": ["{{baseUrl}}"], "path": ["api", "reviews", "seller", ":sellerId"], "variable": [{"key": "sellerId", "value": "{{sellerId}}", "description": "Seller ID"}]}, "description": "Get a list of reviews for a specific seller. Public access."}, "response": []}, {"name": "Create Review (Buyer)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"content\": \"{{contentId}}\",\n    \"rating\": 5,\n    \"title\": \"Excellent Content\",\n    \"text\": \"This content was very helpful and well-produced. Highly recommended!\"\n}"}, "url": {"raw": "{{baseUrl}}/api/reviews", "host": ["{{baseUrl}}"], "path": ["api", "reviews"]}, "description": "Create a new review for content. Buyer access only (must have purchased the content)."}, "response": []}, {"name": "Update Review (Buyer)", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"rating\": 4,\n    \"title\": \"Good Content\",\n    \"text\": \"Updated review: This content was helpful but could be improved in some areas.\"\n}"}, "url": {"raw": "{{baseUrl}}/api/reviews/:id", "host": ["{{baseUrl}}"], "path": ["api", "reviews", ":id"], "variable": [{"key": "id", "value": "{{reviewId}}", "description": "Review ID"}]}, "description": "Update a review. Buyer access only (must be the reviewer)."}, "response": []}, {"name": "Delete Review (Buyer/Admin)", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/reviews/:id", "host": ["{{baseUrl}}"], "path": ["api", "reviews", ":id"], "variable": [{"key": "id", "value": "{{reviewId}}", "description": "Review ID"}]}, "description": "Delete a review. Buyer access only (must be the reviewer) or admin."}, "response": []}], "description": "Endpoints for managing reviews for content and sellers."}, {"name": "Messages Management", "item": [{"name": "Get Conversations", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/messages/conversations", "host": ["{{baseUrl}}"], "path": ["api", "messages", "conversations"]}, "description": "Get a list of conversations for the authenticated user."}, "response": []}, {"name": "Get Single Conversation", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/messages/conversations/:id", "host": ["{{baseUrl}}"], "path": ["api", "messages", "conversations", ":id"], "variable": [{"key": "id", "value": "{{conversationId}}", "description": "Conversation ID"}]}, "description": "Get details of a specific conversation by ID. Access restricted to conversation participants."}, "response": []}, {"name": "Create Conversation", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"recipient\": \"{{userId}}\",\n    \"subject\": \"Question about your content\",\n    \"message\": \"I have a question about your basketball training content.\"\n}"}, "url": {"raw": "{{baseUrl}}/api/messages/conversations", "host": ["{{baseUrl}}"], "path": ["api", "messages", "conversations"]}, "description": "Create a new conversation with another user."}, "response": []}, {"name": "Archive Conversation", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/messages/conversations/:id/archive", "host": ["{{baseUrl}}"], "path": ["api", "messages", "conversations", ":id", "archive"], "variable": [{"key": "id", "value": "{{conversationId}}", "description": "Conversation ID"}]}, "description": "Archive a conversation. Access restricted to conversation participants."}, "response": []}, {"name": "Get Messages in Conversation", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/messages/conversations/:conversationId/messages", "host": ["{{baseUrl}}"], "path": ["api", "messages", "conversations", ":conversationId", "messages"], "variable": [{"key": "conversationId", "value": "{{conversationId}}", "description": "Conversation ID"}]}, "description": "Get a list of messages in a specific conversation. Access restricted to conversation participants."}, "response": []}, {"name": "Send Message in Conversation", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"message\": \"Thanks for your question. I'd be happy to help!\"\n}"}, "url": {"raw": "{{baseUrl}}/api/messages/conversations/:conversationId/messages", "host": ["{{baseUrl}}"], "path": ["api", "messages", "conversations", ":conversationId", "messages"], "variable": [{"key": "conversationId", "value": "{{conversationId}}", "description": "Conversation ID"}]}, "description": "Send a new message in a conversation. Access restricted to conversation participants."}, "response": []}, {"name": "<PERSON> Con<PERSON> as <PERSON>", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/messages/conversations/:conversationId/read", "host": ["{{baseUrl}}"], "path": ["api", "messages", "conversations", ":conversationId", "read"], "variable": [{"key": "conversationId", "value": "{{conversationId}}", "description": "Conversation ID"}]}, "description": "Mark a conversation as read. Access restricted to conversation participants."}, "response": []}, {"name": "Get Unread Messages Count", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/messages/unread-count", "host": ["{{baseUrl}}"], "path": ["api", "messages", "unread-count"]}, "description": "Get the count of unread messages for the authenticated user."}, "response": []}], "description": "Endpoints for managing messages and conversations between users."}, {"name": "Wishlist Management", "item": [{"name": "Get Wishlist", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/wishlist", "host": ["{{baseUrl}}"], "path": ["api", "wishlist"]}, "description": "Get the authenticated user's wishlist."}, "response": []}, {"name": "Add to Wishlist", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"contentId\": \"{{contentId}}\"\n}"}, "url": {"raw": "{{baseUrl}}/api/wishlist", "host": ["{{baseUrl}}"], "path": ["api", "wishlist"]}, "description": "Add a content item to the authenticated user's wishlist."}, "response": []}, {"name": "Remove from Wishlist", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/wishlist/:contentId", "host": ["{{baseUrl}}"], "path": ["api", "wishlist", ":contentId"], "variable": [{"key": "contentId", "value": "{{contentId}}", "description": "Content ID"}]}, "description": "Remove a content item from the authenticated user's wishlist."}, "response": []}, {"name": "Check if Content is in Wishlist", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/wishlist/check/:contentId", "host": ["{{baseUrl}}"], "path": ["api", "wishlist", "check", ":contentId"], "variable": [{"key": "contentId", "value": "{{contentId}}", "description": "Content ID"}]}, "description": "Check if a content item is in the authenticated user's wishlist."}, "response": []}], "description": "Endpoints for managing user wishlists."}, {"name": "Health Check", "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/health", "host": ["{{baseUrl}}"], "path": ["health"]}, "description": "Check the health of the API. Public access."}, "response": []}], "description": "Endpoint for checking the health of the API."}, {"name": "<PERSON><PERSON> Onboarding", "item": [{"name": "<PERSON>ller Onboarding Status", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/users/seller/onboarding", "host": ["{{baseUrl}}"], "path": ["api", "users", "seller", "onboarding"]}, "description": "Get the current onboarding status and completion percentage for the authenticated seller. Returns detailed information about completed and missing fields required for seller verification. Seller access only."}, "response": []}, {"name": "Update Seller Onboarding Info", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n    \"description\": \"Experienced basketball coach with 10+ years of training athletes at all levels. Specialized in shooting techniques and defensive strategies. I have worked with players from high school to professional level, helping them improve their fundamentals and reach their full potential.\",\n    \"profilePic\": \"https://example.com/profile-pics/coach-micha<PERSON>-jordan.jpg\",\n    \"sports\": [\n        \"Basketball\",\n        \"Fitness\",\n        \"Athletic Training\"\n    ],\n    \"expertise\": [\n        \"Shooting Techniques\",\n        \"Defense\",\n        \"Conditioning\",\n        \"Mental Training\",\n        \"Game Strategy\"\n    ],\n    \"certifications\": [\n        \"USA Basketball Certified Coach\",\n        \"NASM Personal Trainer\",\n        \"Sports Psychology Certificate\",\n        \"First Aid & CPR Certified\"\n    ],\n    \"experiences\": [\n        {\n            \"schoolName\": \"State University\",\n            \"position\": \"Assistant Coach\",\n            \"fromYear\": 2018,\n            \"toYear\": 2023\n        },\n        {\n            \"schoolName\": \"Local High School\",\n            \"position\": \"Head Coach\",\n            \"fromYear\": 2015,\n            \"toYear\": 2018\n        },\n        {\n            \"schoolName\": \"Elite Basketball Academy\",\n            \"position\": \"Skills Development Coach\",\n            \"fromYear\": 2012,\n            \"toYear\": 2015\n        }\n    ],\n    \"minTrainingCost\": 75,\n    \"socialLinks\": {\n        \"facebook\": \"https://facebook.com/coach.michael.jordan\",\n        \"linkedin\": \"https://linkedin.com/in/coach-michael-jordan\",\n        \"twitter\": \"https://twitter.com/coach_mj_basketball\"\n    }\n}"}, "url": {"raw": "{{baseUrl}}/api/users/seller/onboarding", "host": ["{{baseUrl}}"], "path": ["api", "users", "seller", "onboarding"]}, "description": "Update seller onboarding information including description, profile picture, sports expertise, certifications, experiences, minimum training cost, and social media links. All fields are optional and will be merged with existing data. Seller access only."}, "response": []}, {"name": "Complete Seller Onboarding", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n    \"description\": \"I have been coaching for 10+ years and specialize in youth sports development.\",\n    \"profilePic\": \"https://example.com/profile.jpg\",\n    \"experiences\": [\n        {\n            \"schoolName\": \"Elite Sports Academy\",\n            \"position\": \"Head Coach\",\n            \"fromYear\": 2015,\n            \"toYear\": 2022\n        },\n        {\n            \"schoolName\": \"National Sports Club\",\n            \"position\": \"Trainer\",\n            \"fromYear\": 2012,\n            \"toYear\": 2014\n        }\n    ],\n    \"minTrainingCost\": 1500,\n    \"socialLinks\": {\n        \"facebook\": \"https://facebook.com/sportsguru\",\n        \"linkedin\": \"https://linkedin.com/in/sportsguru\",\n        \"twitter\": \"https://twitter.com/sportsguru\"\n    },\n    \"sports\": [\n        \"Football\",\n        \"Basketball\"\n    ],\n    \"expertise\": [\n        \"Dribbling\",\n        \"Shooting\",\n        \"Fitness\"\n    ],\n    \"certifications\": [\n        \"AIFF Level 1\",\n        \"NSCA Certified Coach\"\n    ]\n}"}, "url": {"raw": "{{baseUrl}}/api/users/seller/complete-onboarding", "host": ["{{baseUrl}}"], "path": ["api", "users", "seller", "complete-onboarding"]}, "description": "Complete the seller onboarding process by providing all required information and marking it as complete. This endpoint accepts onboarding data, validates all required fields, saves the information, and sets the isOnboardingComplete flag to true. Once completed, the seller can start creating and selling content. Seller access only."}, "response": []}], "description": "Comprehensive seller onboarding endpoints for managing seller profiles, credentials, and verification status. Includes examples for different sports and coaching backgrounds."}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "baseUrl", "value": "http://localhost:5000", "type": "string"}, {"key": "authToken", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4MWFmMGNkOTUzM2RjNzBjZWU3ZGQ3MiIsInJvbGUiOiJhZG1pbiIsImlhdCI6MTc0ODYwNTE0MSwiZXhwIjoxNzQ5MjA5OTQxfQ.Bo1zqk659xK4EKA4Xa_FtPRUJoqqLWkLRCm8pVsg-cI", "type": "string"}, {"key": "userId", "value": "682b171634a42333a3718a2c", "type": "string"}, {"key": "contentId", "value": "", "type": "string"}, {"key": "orderId", "value": "", "type": "string"}, {"key": "bidId", "value": "", "type": "string"}, {"key": "requestId", "value": "", "type": "string"}, {"key": "paymentId", "value": "", "type": "string"}, {"key": "notificationId", "value": "", "type": "string"}, {"key": "cmsPageId", "value": "", "type": "string"}, {"key": "settingId", "value": "", "type": "string"}, {"key": "reviewId", "value": "", "type": "string"}, {"key": "conversationId", "value": "", "type": "string"}]}